#!/usr/bin/env python3
"""
CLEAR-E Real Dataset Experiment Runner

This program runs the complete CLEAR-E evaluation using real energy datasets
(ECL, ETTh1, ETTh2, ETTm1, ETTm2, GEFCom2014) as specified in the paper.

Usage:
    python run_clear_e_experiment.py --dataset ECL --model CLEAR_E --pred_len 24
    python run_clear_e_experiment.py --all_datasets --quick
"""

import argparse
import os
import subprocess
import json
import pandas as pd
from datetime import datetime
import sys

def run_command(cmd, description=""):
    """Execute a command and return results"""
    print(f"\n{'='*80}")
    print(f"Running: {description}")
    print(f"Command: {cmd}")
    print('='*80)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"Error: {result.stderr}")
            return False
        print(f"Success: {result.stdout}")
        return True
    except Exception as e:
        print(f"Exception: {str(e)}")
        return False

def preprocess_datasets():
    """Preprocess all datasets if not already done"""
    print("Checking dataset preprocessing...")
    
    processed_files = [
        'dataset/processed/ecl_processed.csv',
        'dataset/processed/etth1_processed.csv', 
        'dataset/processed/etth2_processed.csv',
        'dataset/processed/ettm1_processed.csv',
        'dataset/processed/ettm2_processed.csv',
        'dataset/processed/gefcom2014_processed.csv'
    ]
    
    missing_files = [f for f in processed_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"Missing processed datasets: {missing_files}")
        return run_command("python dataset/run_preprocessing.py", "Preprocessing datasets")
    
    print("All datasets already preprocessed")
    return True

def run_single_experiment(dataset, model, pred_len, seq_len=96, features='M'):
    """Run a single experiment configuration"""
    
    cmd = f"python run.py --model {model} --data {dataset} --features {features} --seq_len {seq_len} --pred_len {pred_len}"
    
    if model == 'ClearE':
        # Use correct CLEAR-E model name and parameters
        cmd = f"python run.py --model PatchTST --data {dataset} --features {features} --seq_len {seq_len} --pred_len {pred_len}"
        cmd += " --online_method ClearE --concept_dim 64 --bottleneck_dim 32 --ema 0.9"
    elif model == 'Proceed':
        cmd = f"python run.py --model PatchTST --data {dataset} --features {features} --seq_len {seq_len} --pred_len {pred_len}"
        cmd += " --online_method Proceed --concept_dim 64 --bottleneck_dim 32 --ema 0.9"
    
    description = f"{model} on {dataset} with prediction length {pred_len}"
    return run_command(cmd, description)

def run_comprehensive_evaluation():
    """Run comprehensive evaluation across all datasets and models"""
    
    datasets = ['ECL', 'ETTh1', 'ETTh2', 'ETTm1', 'ETTm2']
    models = ['ClearE', 'Proceed', 'PatchTST', 'TCN']
    pred_lengths = [24, 48, 96, 168, 336]
    
    results = []
    
    for dataset in datasets:
        for pred_len in pred_lengths:
            for model in models:
                success = run_single_experiment(dataset, model, pred_len)
                results.append({
                    'dataset': dataset,
                    'model': model,
                    'pred_len': pred_len,
                    'success': success,
                    'timestamp': datetime.now().isoformat()
                })
    
    # Save results summary
    results_df = pd.DataFrame(results)
    results_file = f"results/experiment_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    os.makedirs('results', exist_ok=True)
    results_df.to_csv(results_file, index=False)
    print(f"\nResults saved to: {results_file}")
    
    return results_df

def run_online_experiments():
    """Run online learning experiments with concept drift"""
    
    online_configs = [
        {'dataset': 'ECL', 'model': 'PatchTST', 'online_method': 'ClearE'},
        {'dataset': 'ECL', 'model': 'PatchTST', 'online_method': 'Proceed'},
        {'dataset': 'ETTh1', 'model': 'PatchTST', 'online_method': 'ClearE'},
        {'dataset': 'ETTm1', 'model': 'PatchTST', 'online_method': 'ClearE'},
    ]
    
    for config in online_configs:
        cmd = f"python run.py --data {config['dataset']} --model {config['model']} "
        cmd += f"--seq_len 96 --pred_len 24 --online_method {config['online_method']} "
        cmd += "--online_learning_rate 0.00001"
        
        description = f"Online learning: {config['online_method']} on {config['dataset']}"
        run_command(cmd, description)

def main():
    parser = argparse.ArgumentParser(description='CLEAR-E Real Dataset Experiment Runner')
    parser.add_argument('--dataset', type=str, default='ECL', 
                       choices=['ECL', 'ETTh1', 'ETTh2', 'ETTm1', 'ETTm2', 'GEFCom2014'],
                       help='Dataset to use')
    parser.add_argument('--model', type=str, default='ClearE',
                       choices=['ClearE', 'Proceed', 'PatchTST', 'TCN', 'iTransformer'],
                       help='Model to evaluate')
    parser.add_argument('--pred_len', type=int, default=24, 
                       choices=[24, 48, 96, 168, 336],
                       help='Prediction horizon')
    parser.add_argument('--seq_len', type=int, default=96,
                       help='Input sequence length')
    parser.add_argument('--all_datasets', action='store_true',
                       help='Run evaluation across all datasets')
    parser.add_argument('--online', action='store_true',
                       help='Run online learning experiments')
    parser.add_argument('--quick', action='store_true',
                       help='Run quick evaluation (fewer combinations)')
    parser.add_argument('--preprocess', action='store_true',
                       help='Force dataset preprocessing')
    
    args = parser.parse_args()
    
    print("CLEAR-E Real Dataset Experiment Runner")
    print("="*50)
    
    # Ensure datasets are preprocessed
    if args.preprocess or not preprocess_datasets():
        if not preprocess_datasets():
            print("Failed to preprocess datasets")
            return
    
    if args.all_datasets:
        print("Running comprehensive evaluation across all datasets...")
        results = run_comprehensive_evaluation()
        print(f"\nCompleted {len(results)} experiments")
        
    elif args.online:
        print("Running online learning experiments...")
        run_online_experiments()
        
    elif args.quick:
        print("Running quick evaluation...")
        quick_configs = [
            ('ECL', 'ClearE', 24),
            ('ECL', 'Proceed', 24),
            ('ETTh1', 'ClearE', 96),
            ('ETTm1', 'ClearE', 24),
        ]
        
        for dataset, model, pred_len in quick_configs:
            run_single_experiment(dataset, model, pred_len)
            
    else:
        print(f"Running single experiment: {args.model} on {args.dataset}")
        run_single_experiment(args.dataset, args.model, args.pred_len, args.seq_len)

if __name__ == "__main__":
    main()