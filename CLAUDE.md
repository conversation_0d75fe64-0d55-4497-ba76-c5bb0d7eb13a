# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

CLEAR-E is a framework for energy load forecasting with concept drift adaptation, extending parameter-efficient fine-tuning for energy systems. It includes the PROCEED baseline method from KDD 2025.

## Quick Commands

### Basic Setup
```bash
pip install -r requirements.txt
```

### Training CLEAR-E
```bash
# Basic training
python run.py --model CLEAR_E --data ECL --features M --seq_len 96 --pred_len 24

# With concept drift adaptation
python run.py --model CLEAR_E --data ECL --features M --seq_len 96 --pred_len 24 --online_method ClearE
```

### Running Experiments
```bash
# Quick experiment
python experiments/run_experiments.py --quick

# Comprehensive evaluation
python experiments/run_comprehensive_experiments.py

# Data preprocessing
python dataset/run_preprocessing.py
```

### Model Evaluation
```bash
# Run specific model comparison
python experiments/run_experiments.py --model CLEAR_E --baseline PROCEED

# Generate result tables
python experiments/generate_experiment_tables.py
```

## Core Architecture

### CLEAR-E Framework (`adapter/clear_e.py`)
- **EnergyMetadataEncoder**: Integrates meteorological/calendar metadata with temporal patterns
- **LightweightAdaptGenerator**: Selective adaptation of final prediction layers only
- **DriftMemoryModule**: Maintains adaptation history with regularization
- **EnergyAwareLoss**: Asymmetric loss incorporating domain-specific cost structures

### Key Components

**Models** (`models/`): Baseline forecasting models (PatchTST, Transformer, TCN, etc.)

**Data Pipeline** (`data_provider/`): Handles energy datasets (ECL, GEFCom2014, ETT, etc.)

**Experiment Framework** (`experiments/`): 
- `experimental_framework.py`: Core evaluation logic
- `clear_e_model.py`: CLEAR-E model implementations
- `baseline_models.py`: Comparison baselines

**Adaptation Modules** (`adapter/module/`):
- `base.py`: Base adaptation interface
- `down_up.py`: Down-up adaptation mechanism
- `generator.py`: Parameter generation for adapters
- `ssf.py`: Scale-shift adaptation

## Key Parameters

### CLEAR-E Specific
- `--concept_dim`: Concept encoder dimension (default: 64)
- `--bottleneck_dim`: Adaptation bottleneck dimension (default: 32) 
- `--drift_memory_size`: Memory buffer size (default: 10)
- `--energy_penalty`: Loss penalty weight (default: 1.4)
- `--metadata_dim`: Energy metadata features
- `--target_layers`: Layers to adapt (default: ['projection', 'head', 'output', 'fc'])

### Data Configuration
See `settings.py` for dataset-specific configurations:
- `ECL`: 321 clients, electricity consumption
- `ETT`: Substation monitoring data
- `GEFCom2014`: Competition-grade with weather variables

## Running Scripts

### Script Locations
- **Training**: `scripts/pretrain/` and `scripts/online/`
- **Model-specific**: Organized by model type (PatchTST, TCN_RevIN, etc.)
- **Dataset-specific**: Each dataset has dedicated configuration scripts

### Example Usage
```bash
# PatchTST on ECL with CLEAR-E
bash scripts/online/PatchTST/ClearE/ECL.sh

# TCN on ETTh2 with PROCEED
bash scripts/online/TCN_RevIN/Proceed/ETTh2.sh
```

## Development Workflow

1. **Data Preparation**: Run `dataset/run_preprocessing.py` for processed datasets
2. **Model Training**: Use `run.py` with appropriate arguments
3. **Evaluation**: Use `experiments/run_experiments.py` for comprehensive testing
4. **Analysis**: Results saved in `experiments/results/` and `results/` directories

## File Structure

- **adapter/**: CLEAR-E implementation and adaptation modules
- **data_provider/**: Data loading and preprocessing utilities  
- **models/**: Baseline forecasting models
- **experiments/**: Evaluation framework and result generation
- **layers/**: Neural network components and utilities
- **util/**: Metrics, tools, and helper functions